{"name": "ekb", "private": true, "type": "module", "scripts": {"build": "NODE_OPTIONS='--max-old-space-size=8048' nuxt build", "build:portal": "cp .env.portal .env && NODE_OPTIONS='--max-old-space-size=4096' nuxt build", "dev": "cp .env.locals .env && nuxt dev", "dev:portal": "cp .env.dev-portal .env && nuxt dev", "dev-prd": "cp .env.productions .env && nuxt dev", "generate": "cp .env.productions .env && nuxt generate && rm -rf ../../../../nuxt-generated-file/ekb-generate/_nuxt/  && cp -rf .output/public/* ../../../../nuxt-generated-file/ekb-generate/ && git add . && git commit -m \"update ekb generate\" && git push origin main", "generate-test": "cp .env.test .env && nuxt generate && rm -rf ../../../../nuxt-generated-file/ekb-generate-test/_nuxt/  && cp -rf .output/public/* ../../../../nuxt-generated-file/ekb-generate-test/ ", "generate-public": "cp .env.public .env && nuxt generate && rm -rf ../../../../nuxt-generated-file/ekb-generate/_nuxt/  && cp -rf .output/public/* ../../../../nuxt-generated-file/ekb-generate/ ", "generate-dev": "cp .env.dev .env && nuxt generate && rm -rf ../../../../nuxt-generated-file/ekb-generate-dev/_nuxt/  && cp -rf .output/public/* ../../../../nuxt-generated-file/ekb-generate-dev/", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@ckeditor/ckeditor5-basic-styles": "^40.0.0", "@ckeditor/ckeditor5-build-classic": "^40.0.0", "@ckeditor/ckeditor5-ckfinder": "^41.2.1", "@ckeditor/ckeditor5-editor-classic": "^40.0.0", "@ckeditor/ckeditor5-essentials": "^40.0.0", "@ckeditor/ckeditor5-link": "^40.0.0", "@ckeditor/ckeditor5-paragraph": "^40.0.0", "@ckeditor/ckeditor5-theme-lark": "^40.0.0", "@ckeditor/ckeditor5-upload": "^41.2.1", "@ckeditor/ckeditor5-vue": "^5.1.0", "@ckeditor/vite-plugin-ckeditor5": "^0.1.3", "@handsontable/vue3": "^15.1.0", "@refactorjs/ofetch": "^1.0.8", "@sweetalert2/theme-material-ui": "^5.0.16", "@tanstack/vue-query": "^5.18.1", "cross-env": "^10.0.0", "filepond": "^4.31.1", "filepond-plugin-file-validate-type": "^1.2.9", "filepond-plugin-image-preview": "^4.6.12", "handsontable": "^15.1.0", "nuxt": "^3.10.3", "pdfjs-dist": "^4.6.82", "postcss": "^8.3.3", "pspdfkit": "^2024.2.0", "vite": "^5.3.5", "vue": "^3.4.21", "vue-filepond": "^7.0.4", "vue-router": "^4.3.0", "vue3-perfect-scrollbar": "^1.6.1", "vuetify": "^3.5.1", "xlsx": "^0.18.5"}}